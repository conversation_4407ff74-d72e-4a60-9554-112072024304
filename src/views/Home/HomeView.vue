<template>
  <MainLayout :showNav="showNav">
    <component :is="currentHomeComponent" v-if="currentHomeComponent" />
    <div v-else class="error-container">
      <div class="error-content">
        <div class="error-icon">
          <svg viewBox="0 0 24 24" width="48" height="48" fill="currentColor">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
        </div>
        <h3 class="error-title">未找到对应的业务</h3>
        <p class="error-message">请联系统管理员</p>
      </div>
    </div>
  </MainLayout>
</template>

<script setup>
import { ref, computed, defineAsyncComponent } from 'vue'
import MainLayout from '@components/Common/MainLayout/MainLayout.vue'
import { getBizCode } from '@utils/curEnv.js'

const bizCode = ref(getBizCode())

// 懒加载组件映射表
const componentMap = {
  fupin: defineAsyncComponent(() => import('@views/Home/BFHome/BFHomeView.vue')),
  ziying: defineAsyncComponent(() => import('@views/Home/ZYHome/ZYHomeView.vue')),
  welfaresop: defineAsyncComponent(() => import('@views/Home/WelfareHome/WelfareHomeView.vue')),
  labor: defineAsyncComponent(() => import('@views/Home/LaborHome/LaborHomeView.vue')),
  ygjd: defineAsyncComponent(() => import('@views/Home/YgjdHome/YgjdHomeView.vue')),
  zq: defineAsyncComponent(() => import('@views/Home/ZQHome/ZQHomeView.vue')),
  lnzx: defineAsyncComponent(() => import('@views/Home/LnxzHome/LnzxHomeView.vue')),
  sfzn: defineAsyncComponent(() => import('@views/Home/SfznHome/SfznHomeView.vue'))
}

// 根据bizCode动态获取对应组件
const currentHomeComponent = computed(() => {
  return componentMap[bizCode.value] || null
})

// 控制导航显示：sfzn业务不显示导航，没有对应模块时也不显示导航
const showNav = computed(() => {
  return bizCode.value !== 'sfzn' && currentHomeComponent.value !== null
})
</script>

<style lang="less" scoped>
.error-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #F8F9FA;
  padding: 5px;
}

.error-content {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 48px 40px;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.65);
  max-width: 480px;
  width: 100%;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: var(--wo-biz-theme-gradient-1);
    border-radius: 0 0 4px 4px;
  }
}

.error-icon {
  margin-bottom: 24px;

  svg {
    color: var(--wo-biz-theme-color);
    filter: drop-shadow(0 4px 8px var(--wo-biz-theme-focus));
  }
}

.error-title {
  margin: 0 0 16px 0;
  letter-spacing: -0.5px;
  font-size: 18px;
  font-weight: 700;
  color: #171E24;
  line-height: 1.5;
}

.error-message {
  font-size: 16px;
  font-weight: 500;
  color: #4A5568;
  line-height: 1.5;
  margin: 0 0 20px 0;

  code {
    background: #F8F9FA;
    color: var(--wo-biz-theme-color);
    padding: 4px 8px;
    border-radius: 6px;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 14px;
    font-weight: 600;
  }
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.retry-btn, .contact-btn {
  display: inline-flex; align-items: center; justify-content: center; border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px var(--wo-biz-theme-focus);
  }

  &:active {
    transform: translateY(0);
  }
}

.retry-btn {
  background: var(--wo-biz-theme-gradient-1);
  color: #FFFFFF;

  &:hover {
    background: var(--wo-biz-theme-gradient-3);
    box-shadow: 0 4px 12px var(--wo-biz-theme-bg-4);
  }
}

.contact-btn {
  background: #FFFFFF;
  color: #4A5568;
  border: 1px solid #E2E8EE;

  &:hover {
    background: #F8F9FA;
    border-color: #718096;
  }
}

@media (max-width: 768px) {
  .error-container {
    padding: 16px;
  }

  .error-content {
    padding: 32px 24px;
  }

  .error-title {
    font-size: 20px;
  }

  .error-message {
    font-size: 16px;
  }

  .error-actions {
    flex-direction: column;
    align-items: center;
  }

  .retry-btn, .contact-btn {
    width: 100%;
    max-width: 200px;
  }
}
</style>
