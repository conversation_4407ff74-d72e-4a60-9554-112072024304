<template>
  <div class="lnzx-home">
    <div class="bg" />

    <!-- 头部区域 -->
    <div class="header">
      <img class="title" src="./assets/lnzx.png" alt="联农智选">
      <div class="search-box" @click="onSearchClick">
        <input type="text" placeholder="请搜索商品信息" readonly>
      </div>
    </div>

    <!-- 轮播图区域 -->
    <div class="swiper-box">
      <GoodsSwiper
        v-if="swiperList.length > 0"
        key="banner-content"
        :imageList="swiperList"
        mode="landscape"
        paginationType="fraction"
        :autoplay="true"
        :loop="true"
        @slide-change="onSwipeChange"
        @image-click="onSlideClick"
      >
        <template #pagination>
          <div class="custom-indicator">
            <span class="current">{{ curSwiperIndex + 1 }}</span>
            <span class="separator">/</span>
            <span class="total">{{ swiperList.length }}</span>
          </div>
        </template>
      </GoodsSwiper>
    </div>

    <!-- 图标导航区域 -->
    <div class="icon-list">
      <div
        class="icon-item"
        v-for="item in iconList"
        :key="item.imgUrl"
        @click="onIconItemClick(item)"
      >
        <img class="icon-img" v-lazy="item.imgUrl" :alt="item.chName">
        <div class="icon-name">{{ item.chName }}</div>
      </div>
    </div>

    <!-- 商品列表区域 -->
    <div class="goods-list" v-if="displayTypeList.length > 0">
      <GoodsItemCard
        v-for="(typeItem, index) in displayTypeList"
        :key="typeItem.id"
        :goodsList="goodsList[typeItem.id]"
        :title="typeItem.name"
        :icon="getGoodsListIcon(index)"
        :containerStyle="getGoodsListStyle(index)"
        @goodsClick="onGoodsItemClick"
      />
    </div>

    <!-- 底部占位 -->
    <div class="bottom-spacer" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { isUnicom, isWopay } from 'commonkit'
import GoodsItemCard from './components/GoodsItemCard.vue'
import GoodsSwiper from '@components/Common/GoodsSwiper.vue'
import { getBannerInfo, getIconInfo } from '@/api/interface/bannerIcon'
import { getGoodsList, getPartionList } from '@/api/interface/goods'
import { getBizCode } from '@/utils/curEnv'
import { curChannelBiz } from '@utils/storage.js'


import goodsListPic1 from './assets/goods-list-1.png'
import goodsListPic2 from './assets/goods-list-2.png'
import goodsListPic3 from './assets/goods-list-3.png'
import goodsListPic4 from './assets/goods-list-4.png'
import goodsListPic5 from './assets/goods-list-5.png'
import goodsListPic6 from './assets/goods-list-6.png'
import goodsListPic7 from './assets/goods-list-7.png'
import goodsListPic8 from './assets/goods-list-8.png'


const MAX_DISPLAY_TYPES = 8
const MAX_ICON_COUNT = 4
const GOODS_PAGE_SIZE = 10


const GOODS_LIST_CONFIG = {
  icons: [goodsListPic1, goodsListPic2, goodsListPic3, goodsListPic4, goodsListPic5, goodsListPic6, goodsListPic7, goodsListPic8],
  styles: [
    'background-image: linear-gradient(180deg, #F9E8E6 0%, #FFFFFF 18%);',
    'background-image: linear-gradient(180deg, #E6EAF9 0%, #FFFFFF 18%);',
    'background-image: linear-gradient(180deg, #DDFFF5 0%, #FFFFFF 18%);',
    'background-image: linear-gradient(180deg, #F9E8E6 0%, #FFFFFF 18%);',
    'background-image: linear-gradient(180deg, #E6EAF9 0%, #FFFFFF 18%);',
    'background-image: linear-gradient(180deg, #DDFFF5 0%, #FFFFFF 18%);',
    'background-image: linear-gradient(180deg, #F9E8E6 0%, #FFFFFF 18%);',
    'background-image: linear-gradient(180deg, #E6EAF9 0%, #FFFFFF 18%);'
  ]
}


const filterByChannel = (list) => {
  if (isUnicom) {
    return list.filter(item => item.channelType === '1')
  } else if (isWopay) {
    return list.filter(item => item.channelType === '0')
  } else {
    return list.filter(item => item.channelType === '2')
  }
}


const router = useRouter()


const swiperList = ref([])
const curSwiperIndex = ref(0)
const iconList = ref([])
const typeList = ref([])
const goodsList = reactive({})
const loading = ref(false)


const displayTypeList = computed(() => {
  return typeList.value
    .filter(type => goodsList[type.id] && goodsList[type.id].length > 0)
    .slice(0, MAX_DISPLAY_TYPES)
})


const getGoodsListIcon = (index) => {
  return GOODS_LIST_CONFIG.icons[index % GOODS_LIST_CONFIG.icons.length]
}

const getGoodsListStyle = (index) => {
  return GOODS_LIST_CONFIG.styles[index % GOODS_LIST_CONFIG.styles.length]
}

const getSwiperList = async () => {
  try {
    const [error, json] = await getBannerInfo({
      bizCode: getBizCode('GOODS'),
      showPage: 1
    })
    if (error) {
      console.error('获取轮播图失败:', error)
      return
    }
    swiperList.value = filterByChannel(json).map(item => ({
      type: 'image',
      url: item.imgUrl,
      alt: item.bannerChName,
      linkUrl: item.url,
    }))
  } catch (error) {
    console.error('获取轮播图异常:', error)
  }
}

const getIconList = async () => {
  try {
    const [error, json] = await getIconInfo({
      bizCode: getBizCode('QUERY'),
      channel: curChannelBiz.get(),
      showPage: 9
    })
    if (error) {
      console.error('获取图标列表失败:', error)
      return
    }
    iconList.value = json.slice(0, MAX_ICON_COUNT)
  } catch (error) {
    console.error('获取图标列表异常:', error)
  }
}

const getTypeList = async () => {
  try {
    const [error, json] = await getPartionList({
      bizCode: getBizCode('GOODS'),
      type: 2
    })
    if (error) {
      console.error('获取分类列表失败:', error)
      return
    }
    typeList.value = json ? json.sort((a, b) => a.pos - b.pos) : []


    const promises = typeList.value.map(item => getGoodsListById(item.id))
    await Promise.allSettled(promises)
  } catch (error) {
    console.error('获取分类列表异常:', error)
  }
}

const getGoodsListById = async (id) => {
  try {
    const [error, json] = await getGoodsList({
      type: 'partion',
      bizCode: getBizCode('GOODS'),
      id,
      page_no: 1,
      page_size: GOODS_PAGE_SIZE
    })
    if (error) {
      console.error(`获取商品列表失败 (ID: ${id}):`, error)
      return
    }
    goodsList[id] = json || []
  } catch (error) {
    console.error(`获取商品列表异常 (ID: ${id}):`, error)
  }
}

const onSearchClick = () => {
  router.push('/search')
}

const onSlideClick = (item) => {
  if (item.linkUrl) {
    window.location.href = item.linkUrl
  }
}

const onSwipeChange = ({ index }) => {
  curSwiperIndex.value = index
}

const onIconItemClick = (item) => {
  if (item.url) {
    window.location.href = item.url
  }
}

const onGoodsItemClick = (item) => {
  if (item?.id && item?.skuList?.[0]?.skuId) {
    router.push(`/goodsdetail/${item.id}/${item.skuList[0].skuId}`)
  }
}


const initData = async () => {
  loading.value = true
  try {
    await Promise.allSettled([
      getSwiperList(),
      getIconList(),
      getTypeList()
    ])
  } catch (error) {
    console.error('初始化数据失败:', error)
  } finally {
    loading.value = false
  }
}


onMounted(() => {
  initData()
})
</script>

<style lang='less' scoped>
.lnzx-home {
  position: relative;
  height: 100%;
  background-color: #f5f8fa;
  overflow: auto;

  .bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 165px;
    background-image: linear-gradient(180deg, #00C557 0%, #00C557 46%, rgba(0, 197, 87, 0.00) 100%);
  }

  .header {
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 17px 14px 12px;

    .title {
      flex-shrink: 0;
      margin-right: 14px;
      color: #fff;
      width: 88px;
    }

    .search-box {
      flex-grow: 1;
      overflow: hidden;
      height: 37px;
      background: #fff;
      border-radius: 20px;
      font-size: 0;
      position: relative;

      input {
        padding: 0 17px;
        padding-right: 40px;
        width: 100%;
        height: 100%;
        font-size: 14px;
        outline: none;
        border: none;
        &::-webkit-input-placeholder {
          color: #C5C5C5;
        }
      }

      &::after {
        content: "";
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        width: 16px;
        height: 16px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23C5C5C5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E") center no-repeat;
        background-size: contain;
      }
    }
  }

  .swiper-box {
    position: relative;
    margin: 0 10px;
    z-index: 1;

    .custom-indicator {
      position: absolute;
      right: 10px;
      bottom: 0;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 25px;
      height: 16px;
      line-height: 16px;
      font-size: 12px;
      font-weight: 500;
      transform: scale(0.8);

      &::before {
        content: "";
        position: absolute;
        left: -2px;
        width: 18px;
        height: 18px;
        background-color: #fff;
        border-radius: 50%;
        z-index: -1;
      }

      &::after {
        content: "";
        position: absolute;
        right: -2px;
        width: 18px;
        height: 18px;
        background-color: #fff;
        border-radius: 50%;
        z-index: -1;
      }

      .current {
        color: #333;
      }

      .separator {
        margin: 0 2px;
        color: #999999;
      }

      .total {
        color: #999999;
      }
    }
  }

  .icon-list {
    position: relative;
    z-index: 1;
    display: flex;
    justify-content: space-between;
    margin: 16px 0;
    padding: 0 10px;
    background-color: #f5f8fa;

    .icon-item {
      width: 25%;
      padding: 10px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .icon-img {
        width: 60px;
        height: 60px;
      }

      .icon-name {
        margin-top: 8px;
        font-size: 14px;
        color: #333;
        text-align: center;
        font-weight: 400;
      }

    }
  }

  .goods-list {
    padding-bottom: 10px;
    background-color: #f5f8fa;
  }

  .bottom-spacer {
    height: 50px;
  }
}
</style>
