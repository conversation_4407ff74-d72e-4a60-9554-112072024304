<template>
  <div class="home-layout" :class="homeClass">
    <SearchHeader
      v-if="showModules.search !== false"
      v-model="searchKeyword"
      :placeholder="searchPlaceholder"
      :redirect-to-search="true"
      redirect-url="/search"
      @search="handleSearch"
    />

    <div
      v-if="showModules.banner !== false && (skeletonStates.banner || headerBannerList.length > 0)"
      class="home-banner-container"
    >
      <transition name="skeleton-fade" mode="out-in">
        <BannerSkeleton v-if="skeletonStates.banner" key="banner-skeleton" />
        <GoodsSwiper
          v-else-if="headerBannerList.length > 0"
          key="banner-content"
          :imageList="headerBannerList"
          mode="landscape"
          paginationType="fraction"
          :autoplay="true"
          :loop="true"
          @image-click="handleBannerClick"
        />
      </transition>
    </div>

    <div
      v-if="showModules.gridMenu !== false && (skeletonStates.gridMenu || gridMenuItems.length > 0)"
      class="home-grid-menu-container"
    >
      <transition name="skeleton-fade" mode="out-in">
        <GridMenuSkeleton v-if="skeletonStates.gridMenu" key="grid-skeleton" />
        <IconGrid
          v-else-if="gridMenuItems.length > 0"
          key="grid-content"
          :items="gridMenuItems"
          :columns="gridColumns"
          :display-mode="gridDisplayMode"
          :show-more="true"
          :max-items="10"
          @item-click="handleGridItemClick"
          @more-click="handleMoreClick"
        />
      </transition>
    </div>

    <slot name="additional-content" />

    <slot name="main-content" />
  </div>
</template>

<script setup>
import { ref, toRefs } from 'vue'
import { debounce } from 'lodash-es'
import SearchHeader from '@components/Common/SearchHeader.vue'
import IconGrid from '@views/Home/components/IconGrid.vue'
import BannerSkeleton from '@views/Home/components/Skeleton/BannerSkeleton.vue'
import GridMenuSkeleton from '@views/Home/components/Skeleton/GridMenuSkeleton.vue'
import GoodsSwiper from "@components/Common/GoodsSwiper.vue"

const props = defineProps({
  homeClass: {
    type: String,
    default: ''
  },
  searchPlaceholder: {
    type: String,
    default: '搜索商品'
  },
  headerBannerList: {
    type: Array,
    default: () => []
  },
  gridMenuItems: {
    type: Array,
    default: () => []
  },
  gridColumns: {
    type: Number,
    default: 5
  },
  gridDisplayMode: {
    type: String,
    default: 'grid'
  },
  showModules: {
    type: Object,
    default: () => ({
      search: true,
      banner: true,
      gridMenu: true
    })
  },
  skeletonStates: {
    type: Object,
    default: () => ({
      banner: true,
      gridMenu: true
    })
  }
})

const emit = defineEmits(['search', 'banner-click', 'grid-item-click', 'more-click'])

const {
  homeClass,
  searchPlaceholder,
  headerBannerList,
  gridMenuItems,
  gridColumns,
  gridDisplayMode,
  showModules,
  skeletonStates
} = toRefs(props)

const searchKeyword = ref('')

const handleSearch = debounce(() => {
  emit('search', searchKeyword.value)
}, 300)

const handleBannerClick = ({ item }) => {
  emit('banner-click', { item })
}

const handleGridItemClick = ({ item }) => {
  emit('grid-item-click', { item })
}

const handleMoreClick = () => {
  emit('more-click')
}
</script>

<style scoped lang="less">
.home-layout {
  width: 100vw;
  height: 100%;
  overflow: auto;
  background: #F8F9FA;
  box-sizing: border-box;

  .home-banner-container {
    margin: 8px 0;
    padding: 0 10px;
    border-radius: 12px;
    overflow: hidden;
    box-sizing: border-box;
  }

  .home-grid-menu-container {
    border-radius: 12px;
    margin: 8px 12px;
    box-sizing: border-box;
  }
}

.skeleton-fade-enter-active,
.skeleton-fade-leave-active {
  transition: opacity 0.3s ease;
}

.skeleton-fade-enter-from,
.skeleton-fade-leave-to {
  opacity: 0;
}

.skeleton-fade-enter-to,
.skeleton-fade-leave-from {
  opacity: 1;
}
</style>
