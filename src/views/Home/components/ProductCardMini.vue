<template>
  <div class="goods-card-mini">
    <div class="goods-image">
      <img :src="goodsInfo.image" :alt="goodsInfo.name" loading="lazy" decoding="async" />
    </div>

    <div class="goods-info">
      <div class="goods-name">{{ goodsInfo.name }}</div>
<!--      <div class="goods-spec" v-if="goodsInfo.spec">-->
<!--        {{ goodsInfo.spec }}-->
<!--      </div>-->
      <div class="goods-details">
        <template v-if="isZQBiz && (goodsInfo.highPrice || goodsInfo.lowPrice)">
          <PriceDisplay
            :high-price="goodsInfo.highPrice"
            :low-price="goodsInfo.lowPrice"
            range-label=""
            size="small"
            color="orange"
          />
        </template>
        <template v-else>
          <PriceDisplay :price="goodsInfo.price" size="small" color="orange" />
        </template>
        <span class="goods-sales" v-if="goodsInfo.sales > 0 && !isZQBiz">销量: {{ goodsInfo.sales }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, toRefs } from 'vue'
import { getBizCode } from '@/utils/curEnv'
import PriceDisplay from '@components/Common/PriceDisplay.vue'

// 定义组件props
const props = defineProps({
  goodsInfo: {
    type: Object,
    required: true,
    default: () => ({
      image: '',
      name: '',
      price: 0,
      sales: 0,
      spec: '',
      lowPrice: '',
      highPrice: ''
    })
  }
})

const { goodsInfo } = toRefs(props)

const isZQBiz = computed(() => getBizCode() === 'zq')
</script>

<style scoped lang="less">
.goods-card-mini {
  background: #FFFFFF;
  border-radius: 6px;
  overflow: hidden;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
  width: 100%;
  height: 100%;
  max-width: 160px;
  min-width: 100px;
  display: flex;
  flex-direction: column;
}

.goods-image {
  position: relative;
  width: 100%;
  //height: 120px;
  overflow: hidden;
  background: #F8F9FA;
  flex: 1;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    background-color: #F8F9FA;
  }

  &:hover img {
    transform: scale(1.05);
  }
}

.goods-info {
  padding: 8px;
  background: #FFFFFF;

  .goods-name {
    font-size: 12px;
    font-weight: 400;
    color: #171E24;
    margin: 0 0 6px 0;
    line-height: 1.3;
    display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      word-break: break-all;
    text-decoration: none;
  }

  .goods-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;

    .goods-price {
      color: var(--wo-biz-theme-color);
      font-size: 14px;
      font-weight: 700;
      line-height: 1;

      &::before {
        content: '¥';
        font-size: 11px;
        font-weight: 400;
        margin-right: 1px;
      }
    }

    .goods-price-range {
      display: flex;
      align-items: baseline;
      color: var(--wo-biz-theme-color);
      font-weight: 700;
      flex: 1 1 auto;
      min-width: 0;

      .goods-price-number {
        font-size: 12px;
        position: relative;
        padding-left: 8px;

        &::before {
          content: '¥';
          position: absolute;
          left: 0;
          top: 0;
          font-size: 11px;
          font-weight: 400;
        }
      }

      .goods-price-separator {
        margin: 0 2px;
        color: #718096;
        font-weight: 400;
      }
    }

    .goods-sales {
      color: #718096;
      font-size: 11px;
      line-height: 1;
      white-space: nowrap;
    }
  }

  .goods-spec {
    color: #4A5568;
    font-size: 11px;
    line-height: 1.2;
    background: #F8F9FA;
    padding: 2px 6px;
    border-radius: 2px;
    margin-top: 2px;
    display: inline-block;
    max-width: 100%;
    overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
  }
}
</style>
