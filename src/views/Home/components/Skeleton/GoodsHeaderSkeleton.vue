<template>
  <div class="goods-header-skeleton">
    <div class="skeleton-header-container">
      <div v-for="i in 4" :key="i" class="skeleton-header-item" :class="{ 'skeleton-header-item--active': i === 1 }">
        <div class="skeleton-text"></div>
        <div v-if="i === 1" class="skeleton-active-indicator"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
</script>

<style scoped lang="less">

@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-base {
  background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
}

.goods-header-skeleton {

  width: 100vw;
  position: relative;
  padding-bottom: 10px;
  overflow-x: hidden;
  z-index: 98;

  .skeleton-header-container {

    font-size: 14px;
    height: 25px;
    margin-top: 15px;
    display: flex;
    overflow-x: scroll;

    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      display: none;
    }

    .skeleton-header-item {
      position: relative;
      display: flex;
      align-items: center;

      white-space: nowrap;
      height: 14px;
      line-height: 14px;
      padding: 0 17px;
      

      &:not(:last-child)::before {
        content: "";
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 10px;
        background-color: #e8e8e8;
      }

      .skeleton-text {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;

        width: 48px;
        height: 12px;
        border-radius: 2px;
      }


      .skeleton-active-indicator {
        position: absolute;
        left: 50%;
        bottom: -1px;
        width: 56px;
        height: 6px;
        border-radius: 10px;
        transform: translateX(-50%);
        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);

        animation: none;
      }


      &--active {
        .skeleton-text {
  
          width: 52px;
  
          background: linear-gradient(90deg, #e8e8e8 25%, #d0d0d0 50%, #e8e8e8 75%);
          background-size: 200px 100%;
          animation: skeleton-loading 1.2s ease-in-out infinite;
        }
      }
    }
  }
}


@media (max-width: 375px) {
  .goods-header-skeleton {
    .skeleton-header-container {
      .skeleton-header-item {

        padding: 0 15px;

        .skeleton-text {

          width: 42px;
          height: 11px;
        }

        .skeleton-active-indicator {

          width: 50px;
          height: 5px;
        }

        &--active {
          .skeleton-text {
            width: 46px;
          }
        }
      }
    }
  }
}
</style>
