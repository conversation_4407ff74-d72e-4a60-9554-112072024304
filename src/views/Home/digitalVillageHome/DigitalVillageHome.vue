<template>
  <BaseHomeLayout
    :show-modules="showModulesConfig"
    home-class="dv-home"
    search-placeholder="搜索商品"
    :header-banner-list="headerBannerList"
    :grid-menu-items="gridMenuItems"
    :grid-columns="5"
    :skeleton-states="skeletonStates"
    @search="handleSearch"
    @banner-click="handleBannerClick"
    @grid-item-click="handleGridItemClick"
    @more-click="handleMoreClick"
  >
    <template #additional-content>
      <section
        v-if="shouldShowRecommend"
        class="dv-recommend-section"
      >
        <Transition name="skeleton-fade" mode="out-in">
          <RecommendSkeleton v-if="skeletonStates.recommend" key="recommend-skeleton" />
          <RecommendView
            v-else
            key="recommend-content"
            :hot-goods="hotGoods"
          />
        </Transition>
      </section>
    </template>

    <template #main-content>
      <main class="dv-main-content">
        <WaterfallSection
          class="dv-waterfall"
          :waterfall-goods-list="waterfallGoodsList"
          :waterfall-loading="waterfallLoading"
          :waterfall-finished="waterfallFinished"
          :waterfall-button-can-show="waterfallButtonCanShow"
          :waterfall-render-complete="waterfallRenderComplete"
          :skeleton-states="waterfallSkeletonStates"
          @goods-click="handleGoodsClick"
          @load-more="handleWaterfallLoadMore"
          @after-render="handleWaterfallAfterRender"
        />

        <aside class="dv-order-float">
          <img
            src="./assets/order.png"
            alt="购物订单"
            loading="lazy"
            @click="loadOrderList"
          >
        </aside>

        <WoActionBarPlaceholder :height="85" />
        <WoActionBar class="dv-action-bar">
          <div class="dv-action-content">
            <div class="dv-action-icon" aria-hidden="true"></div>
            <div class="dv-action-text">
              <span class="dv-action-title">本地农产品销售攻略</span>
              <span class="dv-action-subtitle">本地供应商推荐</span>
            </div>
            <WoButton
              type="gradient"
              size="small"
              round
              @click="recommend"
            >
              查看详情
            </WoButton>
          </div>
        </WoActionBar>
      </main>
    </template>
  </BaseHomeLayout>
</template>
<script setup>
import { ref, onMounted, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { debounce } from 'lodash-es'
import BaseHomeLayout from '@views/Home/components/BaseHomeLayout.vue'
import WaterfallSection from '@components/GoodsListCommon/WaterfallSection.vue'
import RecommendView from '@views/Home/components/RecommendView.vue'
import RecommendSkeleton from '@views/Home/components/Skeleton/RecommendSkeleton.vue'
import { useHomeData } from '@views/Home/composables/useHomeData.js'
import { useHomeNavigation } from '@views/Home/composables/useHomeNavigation.js'
import { getHotGoods } from '@/api/interface/digitalVillage'
import { getBizCode } from '@/utils/curEnv'
import { curChannelBiz } from '@/utils/storage'
import { isUnicom } from 'commonkit'
import WoActionBarPlaceholder from '@components/WoElementCom/WoActionBarPlaceholder.vue'
import WoActionBar from '@components/WoElementCom/WoActionBar.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import { closeToast, showLoadingToast } from 'vant'
import { useUserStore } from '@/store/modules/user'

const {
  headerBannerList,
  gridMenuItems,
  skeletonStates,
  moduleDataReady,
  waterfallGoodsList,
  waterfallLoading,
  waterfallFinished,
  waterfallButtonCanShow,
  waterfallRenderComplete,
  getHeaderBannerList,
  getWaterfallList,
  resetWaterfallState,
  getPartionListData,
  hideSkeletonInOrder
} = useHomeData()

const {
  handleGoodsClick,
  handleBannerClick,
  handleGridItemClick,
  handleMoreClick,
  handleSearch,
} = useHomeNavigation()

const router = useRouter()
const userStore = useUserStore()

const hotGoods = ref([])
const typeList = ref([])
const goodsPoolIdSelected = ref('')

const showModulesConfig = computed(() => ({
  search: false,
  banner: true,
  gridMenu: false
}))

const shouldShowRecommend = computed(() =>
  skeletonStates.value.recommend || hotGoods.value.length > 0
)

const waterfallSkeletonStates = computed(() => ({
  waterfall: skeletonStates.value.waterfall
}))

skeletonStates.value = {
  ...skeletonStates.value,
  recommend: true
}

moduleDataReady.value = {
  ...moduleDataReady.value,
  recommend: false
}

const loadOrderList = debounce(async () => {
  await userStore.queryLoginStatus()
  if (userStore.isLogin) {
    router.push({ path: '/user/order/list' })
  } else {
    await userStore.login({ reload: false })
  }
}, 300)

const recommend = () => {
  router.push('/digitalVillage/znShop')
}

const handleWaterfallAfterRender = () => {
  waterfallRenderComplete.value = true
}

const handleWaterfallLoadMore = debounce(() => {
  getWaterfallList(goodsPoolIdSelected.value, '', true)
}, 200)

const changeGoodsPool = (id, sortType = '') => {
  goodsPoolIdSelected.value = id
  resetWaterfallState()
  getWaterfallList(id, sortType, false)
}

const initPage = async () => {
  try {
    const partionList = await getPartionListData(2)
    typeList.value = partionList

    if (typeList.value.length > 0) {
      const firstRecommend = typeList.value[0]
      goodsPoolIdSelected.value = firstRecommend.id
      changeGoodsPool(firstRecommend.id)
    }
  } catch (error) {
    console.error('初始化页面数据失败:', error)
  }
}

const initHotGoods = async () => {
  const params = {
    showPage: '1',
    bizCode: getBizCode('GOODS'),
    channel: curChannelBiz.get()
  }

  if (!isUnicom) {
    showLoadingToast()
  }

  try {
    const [err, json] = await getHotGoods(params)

    if (err) {
      console.error('获取热门商品失败:', err.msg)
      hotGoods.value = []
    } else {
      hotGoods.value = json || []
    }
  } catch (error) {
    console.error('获取热门商品异常:', error)
    hotGoods.value = []
  } finally {
    if (!isUnicom) {
      closeToast()
    }
    moduleDataReady.value.recommend = true
    await nextTick()
    await hideSkeletonInOrder(['banner', 'gridMenu', 'recommend'])
  }
}

onMounted(async () => {
  await Promise.allSettled([
    getHeaderBannerList(),
    initHotGoods(),
    initPage()
  ])
})
</script>


<style scoped lang="less">
.dv-home {
  min-height: 100vh;

  .dv-recommend-section {
    padding: 0 10px;
    position: relative;
    box-sizing: border-box;
  }

  .dv-main-content {
    position: relative;
  }

  .dv-waterfall {
    :deep(.home-waterfall-container) {
      padding: 0 10px;
    }
  }

  .dv-order-float {
    position: fixed;
    width: 100px;
    height: auto;
    right: 0;
    top: 150px;
    z-index: 999;

    img {
      width: 100%;
      height: auto;
      cursor: pointer;
      transition: transform 0.2s ease;

      &:hover {
        transform: scale(1.05);
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }

  .dv-action-bar {
    padding: 0;

    .dv-action-content {
      background: #ffffff;
      box-shadow: 0 -9px 32px 0 rgba(0, 0, 0, 0.07);
      min-height: 65px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 12px;
      padding: 0 12px;
    }

    .dv-action-icon {
      background-image: url("./assets/fupin.png");
      height: 54px;
      width: 54px;
      background-repeat: no-repeat;
      background-size: contain;
      background-position: center;
      flex-shrink: 0;
    }

    .dv-action-text {
      display: flex;
      flex-direction: column;
      justify-content: center;
      flex: 1;
      min-width: 0;
    }

    .dv-action-title {
      font-size: 16px;
      color: #00200a;
      font-weight: 500;
      line-height: 1.2;
    }

    .dv-action-subtitle {
      font-size: 12px;
      color: #828282;
      font-weight: 400;
      margin-top: 4px;
      line-height: 1.2;
    }

    :deep(.wo-button) {
      flex-shrink: 0;
    }
  }
}

// 骨架屏过渡动画
.skeleton-fade-enter-active,
.skeleton-fade-leave-active {
  transition: opacity 0.3s ease;
}

.skeleton-fade-enter-from,
.skeleton-fade-leave-to {
  opacity: 0;
}

.skeleton-fade-enter-to,
.skeleton-fade-leave-from {
  opacity: 1;
}

// 响应式优化
@media (max-width: 375px) {
  .dv-home {
    .dv-action-content {
      gap: 8px;
      padding: 0 8px;
    }

    .dv-action-icon {
      width: 48px;
      height: 48px;
    }

    .dv-action-title {
      font-size: 14px;
    }

    .dv-action-subtitle {
      font-size: 11px;
    }
  }
}
</style>
